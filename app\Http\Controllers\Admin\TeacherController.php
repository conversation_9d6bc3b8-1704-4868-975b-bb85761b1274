<?php

namespace App\Http\Controllers\Admin;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Http\Requests\TeacherRequests\TeacherFilterRequest;
use App\Http\Requests\TeacherRequests\TeacherStoreRequest;
use App\Http\Requests\TeacherRequests\TeacherUpdateRequest;
use App\Services\TeacherService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class TeacherController extends Controller
{
    /**
     * TeacherController constructor
     */
    public function __construct(protected TeacherService $teacherService)
    {
    }

    /**
     * Display a listing of teachers
     */
    public function index(TeacherFilterRequest $request): View|JsonResponse
    {
        $teachers = $this->teacherService->getAll($request->validated());

        if ($request->ajax()) {
            return $this->formatTeachersForDatatable($teachers);
        }

        return view('admin.pages.teacher.index', [
            'statuses' => UserStatus::dropdown(),
            'teachers' => $teachers,
            'roles' => array_combine(
                RoleEnum::teacher(),
                array_map(fn($role) => RoleEnum::from($role)->label(), RoleEnum::teacher())
            ),
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Format response for DataTables.
     */
    private function formatTeachersForDatatable($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->editColumn('user.name', fn($row) => $row->user->name ?? '-')
            ->editColumn('user.email', fn($row) => $row->user->email ?? '-')
            ->editColumn('user.role', fn($row) => $row->user->roleLabel() ?? '-')
            ->editColumn('gender', function ($row) {
                return $row->gender?->label() ?? '-';
            })
            ->editColumn('birth_date', fn($row) => $row->birth_date ? date('d/m/Y', strtotime($row->birth_date)) : '-')
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn(
                'action',
                fn($row) => view('admin.pages.teacher._action', [
                    'id' => $row->id,
                    'edit' => route('admin.teachers.edit', $row->id),
                    'destroy' => route('admin.teachers.destroy', $row->id),
                ])->render()
            )
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new teacher.
     */
    public function create(): View
    {
        return view('admin.pages.teacher.create', [
            'roles' => array_combine(
                RoleEnum::teacher(),
                array_map(fn($role) => RoleEnum::from($role)->label(), RoleEnum::teacher())
            ),
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created teacher.
     */
    public function store(TeacherStoreRequest $request): JsonResponse
    {
        try {
            $teacher = $this->teacherService->create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dibuat.',
                'data' => $teacher,
            ], 201); // 201 Created
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat data guru.',
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified teacher.
     */
    public function edit(int $id): View
    {
        try {
            $teacher = $this->teacherService->findById($id);

            return view('admin.pages.teacher.edit', [
                'teacher' => $teacher,
                'roles' => array_combine(
                    RoleEnum::teacher(),
                    array_map(fn($role) => RoleEnum::from($role)->label(), RoleEnum::teacher())
                ),
                'statuses' => UserStatus::dropdown(),
                'currentRole' => $teacher->user->getRoleNames()->first(),
            ]);
        } catch (BusinessLogicException $e) {
            abort(404, $e->getMessage());
        }
    }

    /**
     * Update the specified teacher.
     */
    public function update(TeacherUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $this->teacherService->update($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil diperbarui.',
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data guru.',
            ], 500);
        }
    }

    /**
     * Remove the specified teacher.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->teacherService->deleteTeacher($id);

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dihapus.',
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data guru.',
            ], 500);
        }
    }
}

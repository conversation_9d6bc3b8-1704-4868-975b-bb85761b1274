<?php

namespace App\Services;

use App\Repositories\Contracts\StaffRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\StaffException;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class StaffService
{
    protected const MAX_ADMIN_COUNT = 5; // Batasan yang sudah kita tetapkan

    public function __construct(
        protected StaffRepositoryInterface $staffRepository,
        protected UserService $userService
    ) {
    }

    /**
     * Get all staff with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->staffRepository->getAll($filters);
    }

    /**
     * Find a staff member by ID.
     * Converts ModelNotFoundException to BusinessLogicException for consistency.
     */
    public function findById(int $id): User
    {
        try {
            $staff = $this->staffRepository->findById($id);

            return $staff;
        } catch (BusinessLogicException) {
            throw new BusinessLogicException("Staf dengan ID {$id} tidak ditemukan.");
        }
    }

    /**
     * Create a new staff member.
     */
    public function create(array $data): User
    {
        return DB::transaction(function () use ($data) {
            try {
                // Validasi peran staf dan batasan jumlah admin/principal/treasurer
                $this->validateStaffRoleConstraints($data['role'], null);

                // Buat user melalui UserService, yang sudah menangani hashing password dan penugasan peran
                $user = $this->userService->create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => $data['password'],
                    'phone_number' => $data['phone_number'] ?? null,
                    'status' => $data['status'] ?? UserStatus::Active->value,
                    'role' => $data['role'],
                    'username' => $data['username'] ?? strtolower(explode('@', $data['email'])[0]),
                ]);

                return $user->load('roles'); // Muat ulang role untuk memastikan terload
            } catch (BusinessLogicException | StaffException $e) {
                throw $e; // Lempar ulang pengecualian bisnis yang sudah spesifik
            } catch (Throwable $e) {
                // Tangkap pengecualian umum lainnya sebagai BusinessLogicException
                throw new BusinessLogicException('Gagal membuat data staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Update staff information.
     */
    public function update(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            try {
                $staff = $this->findById($id); // Memastikan staf ada (melempar BusinessLogicException jika tidak)

                // Jika peran diubah, validasi batasan
                if (isset($data['role'])) {
                    $this->validateStaffRoleConstraints($data['role'], $staff->id);
                }

                return $this->userService->update($id, $data);
            } catch (BusinessLogicException | StaffException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal memperbarui data staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Delete a staff member.
     */
    public function delete(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            try {
                $staff = $this->findById($id); // Memastikan staf ada

                // Validasi bisnis untuk penghapusan staf
                $this->validateStaffDeletion($staff); // Menerima objek User/Staff

                // Hapus user melalui UserService
                return $this->userService->delete($id);
            } catch (BusinessLogicException | StaffException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal menghapus data staf: ' . $e->getMessage());
            }
        });
    }

    /**
     * Validate staff role constraints (e.g., single principal, max admins).
     * Ini adalah logika bisnis utama untuk penugasan peran staf.
     *
     * @throws StaffException|BusinessLogicException
     */
    protected function validateStaffRoleConstraints(string $role, ?int $excludeUserId = null): void
    {
        // Pengecekan apakah peran diizinkan untuk staf
        if (!in_array($role, RoleEnum::staff())) {
            throw StaffException::invalidRole($role);
        }

        // Pengecekan Principal (hanya satu Principal)
        if ($role === RoleEnum::PRINCIPAL->value) {
            $existingPrincipalCount = $this->staffRepository->countByRole(RoleEnum::PRINCIPAL->value);
            // Jika ada principal lain yang bukan user yang sedang diupdate
            if ($existingPrincipalCount > 0 && ($excludeUserId === null || $existingPrincipalCount > ($excludeUserId ? 1 : 0))) {
                throw StaffException::principalAlreadyExists();
            }
        }

        // Pengecekan Treasurer (hanya satu Treasurer)
        if ($role === RoleEnum::TREASURER->value) {
            $existingTreasurerCount = $this->staffRepository->countByRole(RoleEnum::TREASURER->value);
            // Jika ada treasurer lain yang bukan user yang sedang diupdate
            if ($existingTreasurerCount > 0 && ($excludeUserId === null || $existingTreasurerCount > ($excludeUserId ? 1 : 0))) {
                throw StaffException::treasurerAlreadyExists();
            }
        }

        // Pengecekan Admin (maksimal 5 admin)
        if ($role === RoleEnum::ADMIN->value) {
            $adminCount = $this->staffRepository->countByRole(RoleEnum::ADMIN->value);
            // Jika sedang membuat atau jika update akan menambah admin melebihi batas (dan user bukan admin sebelumnya)
            if ($adminCount >= self::MAX_ADMIN_COUNT && ($excludeUserId === null || ($adminCount >= self::MAX_ADMIN_COUNT && !$this->userRepository->findById($excludeUserId)->hasRole(RoleEnum::ADMIN->value)))) {
                // Periksa juga apakah user yang di-update BUKAN admin sebelumnya
                if ($excludeUserId && $this->userRepository->findById($excludeUserId)->hasRole(RoleEnum::ADMIN->value)) {
                    // Jika user yang di-update sudah admin, tidak masalah
                } else {
                    // Jika user yang di-update bukan admin, dan batas tercapai
                    throw StaffException::maxAdminsReached(self::MAX_ADMIN_COUNT);
                }
            }
        }
    }

    /**
     * Validate if a staff member can be deleted.
     *
     * @throws StaffException|BusinessLogicException
     */
    protected function validateStaffDeletion(User $staff): void
    {
        // Tidak dapat menghapus diri sendiri
        if ($staff->id === Auth::id()) {
            throw new BusinessLogicException('Tidak dapat menghapus akun sendiri.');
        }

        // Tidak dapat menghapus jika statusnya aktif
        if ($staff->status === UserStatus::Active->value) {
            throw StaffException::cannotDeleteActiveStaff();
        }

        // Tidak dapat menghapus admin terakhir
        if ($staff->hasRole(RoleEnum::ADMIN->value)) {
            $activeAdminCount = $this->staffRepository->countByRoleAndStatus(RoleEnum::ADMIN->value, UserStatus::Active->value);
            if ($activeAdminCount <= 1) {
                throw StaffException::cannotDeleteLastAdmin();
            }
        }
    }
}

# Planning Migrasi dari Service Repository Pattern ke MVC Pattern

## 📋 Overview

Proyek Rawooh-v2 saat ini menggunakan Service Repository pattern yang kompleks dengan:
- 14+ Repository Interfaces & Implementations
- 15+ Service Classes
- Dependency Injection yang rumit
- Separation of concerns yang berle<PERSON>han untuk skala aplikasi

**<PERSON><PERSON><PERSON>:** Menyederhanakan arsitektur menjadi MVC pattern tradional Laravel yang lebih mudah dipahami dan dimaintain.

## 🎯 Target Arsitektur Baru

### Sebelum (Service Repository Pattern)
```
Controller → Service → Repository → Model
```

### Sesudah (MVC Pattern)
```
Controller → Model (dengan business logic)
```

## 📊 Analisis Kode Saat Ini

### Services yang Perlu Dimigrasi:
- StudentService
- TeacherService  
- StaffService
- ClassroomService
- SubjectService
- AcademicYearService
- AttendanceService
- LeaveRequestService
- TeacherAssignmentService
- ClassScheduleService
- UserService
- ProgramService
- ShiftService
- LessonHourService

### Repositories yang Akan Dihapus:
- Semua interface di `app/Repositories/Contracts/`
- Semua implementasi di `app/Repositories/Eloquent/`
- Binding di `AppServiceProvider.php`

## 🚀 Fase Migrasi

## Fase 1: Persiapan dan Backup

### 1.1 Backup dan Version Control
- [ ] Buat branch baru: `git checkout -b feature/migrate-to-mvc`
- [ ] Backup database: `php artisan db:backup`
- [ ] Dokumentasi struktur saat ini
- [ ] Commit semua perubahan pending

### 1.2 Setup Testing Environment
- [ ] Pastikan semua test existing berjalan
- [ ] Setup test database terpisah
- [ ] Buat baseline performance metrics

### 1.3 Analisis Dependencies
- [ ] Mapping semua dependencies antar services
- [ ] Identifikasi business logic yang akan dipindah ke models
- [ ] Buat daftar prioritas migrasi (mulai dari yang paling sederhana)

## Fase 2: Refactoring Models

### 2.1 Perkuat Eloquent Models
Pindahkan business logic dari Services ke Models menggunakan:

#### Model Methods untuk Business Logic
```php
// Contoh: Student Model
class Student extends Model 
{
    // Pindahkan dari StudentService
    public function enrollToClassroom($classroomId, $academicYearId) 
    {
        // Business logic enrollment
    }
    
    public function getActiveStudents() 
    {
        return static::whereHas('user', function($q) {
            $q->where('status', 'active');
        })->get();
    }
    
    public function calculateAttendanceRate($academicYearId) 
    {
        // Logic perhitungan attendance
    }
}
```

#### Scopes untuk Query Logic
```php
// Pindahkan filtering dari Repository ke Model Scopes
public function scopeActive($query) 
{
    return $query->whereHas('user', function($q) {
        $q->where('status', 'active');
    });
}

public function scopeByClassroom($query, $classroomId) 
{
    return $query->whereHas('classrooms', function($q) use ($classroomId) {
        $q->where('classroom_id', $classroomId);
    });
}
```

### 2.2 Model Events untuk Side Effects
```php
// Gunakan Model Events untuk logic yang sebelumnya di Service
protected static function booted()
{
    static::creating(function ($student) {
        // Logic yang sebelumnya di StudentService::create()
    });
    
    static::deleting(function ($student) {
        // Cleanup logic
    });
}
```

### 2.3 Custom Collections
```php
// Untuk logic kompleks yang melibatkan multiple models
class StudentCollection extends Collection 
{
    public function getByClassroom($classroomId) 
    {
        return $this->filter(function($student) use ($classroomId) {
            return $student->classrooms->contains('id', $classroomId);
        });
    }
}
```

## Fase 3: Refactoring Controllers

### 3.1 Ubah Dependency Injection
```php
// Sebelum
class StudentController extends Controller 
{
    public function __construct(
        StudentService $studentService,
        UserService $userService,
        ClassroomService $classroomService
    ) {
        // Multiple service dependencies
    }
}

// Sesudah  
class StudentController extends Controller 
{
    // Tidak ada dependency injection, langsung gunakan Model
    public function index(Request $request) 
    {
        $students = Student::with('user', 'classrooms')
            ->when($request->status, fn($q) => $q->active())
            ->when($request->classroom_id, fn($q) => $q->byClassroom($request->classroom_id))
            ->paginate(15);
            
        return view('admin.students.index', compact('students'));
    }
}
```

### 3.2 Simplifikasi Controller Methods
```php
// Sebelum (menggunakan Service)
public function store(StudentStoreRequest $request) 
{
    $this->studentService->create($request->validated());
    return redirect()->back()->with('success', 'Student created');
}

// Sesudah (langsung ke Model)
public function store(StudentStoreRequest $request) 
{
    Student::create($request->validated());
    return redirect()->back()->with('success', 'Student created');
}
```

### 3.3 Handle Complex Operations
Untuk operasi kompleks, gunakan:
- **Model Methods** untuk single model operations
- **Database Transactions** untuk multi-model operations
- **Jobs** untuk background processing
- **Events** untuk decoupled side effects

## Fase 4: Cleanup dan Testing

### 4.1 Hapus Services dan Repositories
- [ ] Hapus folder `app/Services/`
- [ ] Hapus folder `app/Repositories/`
- [ ] Bersihkan `AppServiceProvider.php`
- [ ] Update `composer.json` autoload jika perlu

### 4.2 Update Tests
- [ ] Refactor unit tests untuk test Model methods
- [ ] Update feature tests untuk test Controller langsung
- [ ] Hapus service/repository mocks
- [ ] Tambah database factories jika belum ada

### 4.3 Validation dan Error Handling
- [ ] Pindahkan custom exceptions ke Model level
- [ ] Update error handling di Controllers
- [ ] Pastikan validation tetap di Form Requests

## Fase 5: Optimisasi dan Dokumentasi

### 5.1 Performance Optimization
- [ ] Review N+1 queries dengan `debugbar`
- [ ] Optimasi eager loading di Controllers
- [ ] Add database indexes jika diperlukan
- [ ] Cache frequently accessed data

### 5.2 Code Quality
- [ ] Run `php artisan insights` untuk code quality
- [ ] Update PHPDoc comments
- [ ] Ensure PSR-12 compliance
- [ ] Run static analysis tools

### 5.3 Documentation Update
- [ ] Update README.md
- [ ] Buat dokumentasi arsitektur baru
- [ ] Update API documentation
- [ ] Buat migration guide untuk team

## ⚠️ Risiko dan Mitigasi

### Risiko Tinggi:
1. **Data Loss** - Mitigasi: Backup database sebelum migrasi
2. **Breaking Changes** - Mitigasi: Comprehensive testing
3. **Performance Degradation** - Mitigasi: Performance monitoring

### Risiko Sedang:
1. **Team Confusion** - Mitigasi: Training dan dokumentasi
2. **Regression Bugs** - Mitigasi: Extensive testing
3. **Deployment Issues** - Mitigasi: Staging environment testing

## 📈 Success Metrics

- [ ] Pengurangan 70%+ lines of code
- [ ] Waktu development feature baru berkurang 50%
- [ ] Test coverage tetap >80%
- [ ] Performance tidak menurun >10%
- [ ] Zero critical bugs dalam 2 minggu post-deployment

## 🔄 Rollback Plan

Jika migrasi gagal:
1. Revert ke branch sebelumnya
2. Restore database backup
3. Deploy versi stabil terakhir
4. Analisis penyebab kegagalan
5. Perbaiki dan coba lagi

## 📅 Timeline Estimasi

- **Fase 1:** 2-3 hari
- **Fase 2:** 5-7 hari  
- **Fase 3:** 3-5 hari
- **Fase 4:** 2-3 hari
- **Fase 5:** 1-2 hari

**Total:** 13-20 hari kerja

## 🎯 Next Steps

1. Review dan approve planning ini
2. Setup development environment
3. Mulai dengan Fase 1
4. Daily progress review
5. Weekly milestone evaluation

---

*Planning ini dibuat berdasarkan analisis kodebase Rawooh-v2 dan best practices Laravel MVC pattern.*

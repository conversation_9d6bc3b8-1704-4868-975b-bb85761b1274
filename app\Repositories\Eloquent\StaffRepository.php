<?php

namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Repositories\Contracts\StaffRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

class StaffRepository implements StaffRepositoryInterface
{
    protected UserRepositoryInterface $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Get roles that are considered staff.
     */
    public function getAllowedRoles(): array
    {
        return array_filter(
            RoleEnum::values(),
            fn($role) => !str_contains($role, 'teacher') && !str_contains($role, 'student')
        );
    }

    /**
     * Get all staff members with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        $staffRoles = $this->getAllowedRoles();
        $filters['role_in'] = array_merge($filters['role_in'] ?? [], $staffRoles);

        return $this->userRepository->getAll($filters);
    }

    /**
     * Get all active staff members.
     */
    public function getAllActive(): Collection
    {
        return $this->getAll(['status' => UserStatus::Active->value]);
    }

    /**
     * Find a staff member by ID.
     */
    public function findById(int $id): User
    {
        try {
            $user = $this->userRepository->findById($id);

            if (!$user->hasAnyRole($this->getAllowedRoles())) {
                throw new BusinessLogicException("User dengan ID {$id} bukan merupakan staff."); // Jika user bukan staff
            }

            return $user;
        } catch (BusinessLogicException) {
            throw new BusinessLogicException("User dengan ID {$id} tidak ditemukan."); // Jika user tidak ditemukan
        }
    }

    /**
     * Create a new staff member (which is essentially a new User).
     */
    public function create(array $data): User
    {
        try {
            return $this->userRepository->create($data);
        } catch (Throwable $e) {
            throw $e;
        }
    }

    /**
     * Update an existing staff member (User).
     */
    public function update(int $id, array $data): bool
    {
        return $this->userRepository->update($id, $data);
    }

    /**
     * Delete a staff member (User).
     */
    public function delete(int $id): bool
    {
        return $this->userRepository->delete($id);
    }

    /**
     * Count total number of staff members.
     */
    public function count(): int
    {
        return $this->getAll(['status' => null])->count();
    }

    /**
     * Count staff members by role.
     */
    public function countByRole(string $role): int
    {
        if (!in_array($role, $this->getAllowedRoles())) {
            return 0;
        }
        return $this->userRepository->countUsersByRole($role);
    }

    /**
     * Count staff members by role and status.
     */
    public function countByRoleAndStatus(string $role, string $status): int
    {
        if (!in_array($role, $this->getAllowedRoles())) {
            return 0;
        }
        return $this->userRepository->getAll(['role' => $role, 'status' => $status])->count();
    }
}
